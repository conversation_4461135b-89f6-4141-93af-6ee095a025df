#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序02：验证连板金字塔完整修复效果
测试所有连板级别是否都能正确显示个股信息
"""

import requests
import json
import time
from datetime import datetime

def test_pyramid_display():
    """测试金字塔显示的完整性"""
    print("=" * 60)
    print("测试程序02：验证连板金字塔完整修复效果")
    print("=" * 60)
    
    # 测试API端点
    api_url = "http://localhost:8080/api/lianban_progress"
    
    try:
        print(f"🔍 正在请求API: {api_url}")
        response = requests.get(api_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API请求成功，状态码: {response.status_code}")
            
            # 检查数据结构
            if data.get('success'):
                latest_stocks = data.get('latest_stocks', {})
                
                if latest_stocks:
                    print("\n📊 各连板级别详细信息:")
                    
                    # 定义期望的连板级别顺序
                    expected_levels = ['首板', '1进2', '2进3', '3进4', '4进5', '5进6']
                    
                    total_stocks = 0
                    levels_with_stocks = 0
                    
                    for level in expected_levels:
                        if level in latest_stocks:
                            stocks = latest_stocks[level]
                            if stocks:
                                stock_count = len(stocks)
                                total_stocks += stock_count
                                levels_with_stocks += 1
                                
                                # 统计成功、失败、炸板股票
                                success_count = len([s for s in stocks if s.get('status') == '成'])
                                fail_count = len([s for s in stocks if s.get('status') == '败'])
                                bomb_count = len([s for s in stocks if s.get('status') == '炸'])
                                
                                print(f"   📈 {level}: {stock_count}只股票")
                                print(f"      ✅ 成功: {success_count}只")
                                print(f"      ❌ 失败: {fail_count}只") 
                                print(f"      💥 炸板: {bomb_count}只")
                                
                                # 显示前3只股票作为样本
                                print(f"      📋 样本股票:")
                                for i, stock in enumerate(stocks[:3]):
                                    name = stock.get('name', '未知')
                                    change = stock.get('change_percent', '0')
                                    status = stock.get('status', '未知')
                                    print(f"         {i+1}. {name} {change}% ({status})")
                                
                                if len(stocks) > 3:
                                    print(f"         ... 还有{len(stocks)-3}只股票")
                                print()
                            else:
                                print(f"   📈 {level}: 0只股票")
                        else:
                            print(f"   📈 {level}: 数据缺失")
                    
                    print(f"📊 总结:")
                    print(f"   🎯 有数据的级别: {levels_with_stocks}/{len(expected_levels)}")
                    print(f"   📈 总股票数: {total_stocks}")
                    
                    # 验证修复效果
                    if levels_with_stocks >= 4:  # 至少4个级别有数据
                        print(f"\n✅ 修复验证成功！")
                        print(f"   ✓ 多个连板级别都有股票数据")
                        print(f"   ✓ 金字塔应该能正确显示所有级别")
                        print(f"   ✓ 不再只显示5进6级别")
                        return True
                    else:
                        print(f"\n⚠️  修复可能不完整")
                        print(f"   ⚠️  只有{levels_with_stocks}个级别有数据")
                        return False
                        
                else:
                    print("❌ 没有最新股票数据")
                    return False
                    
            else:
                print(f"❌ API返回失败: {data}")
                return False
                
        else:
            print(f"❌ API请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_ui_interaction():
    """测试UI交互功能"""
    print("\n" + "=" * 60)
    print("UI交互测试建议")
    print("=" * 60)
    
    print("请手动验证以下功能:")
    print("1. 🌐 访问 http://localhost:8080")
    print("2. 📊 查看连板金字塔是否显示所有级别")
    print("3. 🔍 检查每个级别是否显示具体股票信息")
    print("4. 🖱️  点击各级别的 '+' 按钮测试展开功能")
    print("5. 🖱️  点击各级别的 '−' 按钮测试收起功能")
    print("6. 📱 检查股票信息是否包含:")
    print("   - 股票名称")
    print("   - 涨跌幅")
    print("   - 状态（成功/失败/炸板）")

def main():
    """主函数"""
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试API数据
    api_success = test_pyramid_display()
    
    # UI交互测试建议
    test_ui_interaction()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    if api_success:
        print("🎉 连板金字塔修复成功！")
        print("✅ 所有连板级别都能正确显示个股信息")
        print("✅ 不再只显示5进6级别的问题已解决")
    else:
        print("❌ 修复可能存在问题，请检查")
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
