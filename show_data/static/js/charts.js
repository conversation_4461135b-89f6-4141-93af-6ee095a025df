// 股票数据可视化图表脚本

// 全局变量
let lianbanChart, marketChart, limitChart, volumeChart;
let lianbanRealtimeChart, marketRealtimeChart, limitRealtimeChart, volumeRealtimeChart;
let lianbanMergedChart, marketMergedChart, limitMergedChart, volumeMergedChart;
let currentLianbanData = null;
let currentMarketData = null;
let currentLimitData = null;
let currentVolumeData = null;
let currentLianbanView = 'main';
let realtimeDataCache = {
    lianban: [],
    market: [],
    limit: [],
    volume: []
};

// 颜色配置
const colors = {
    primary: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40',
    purple: '#6f42c1',  // 紫色，用于翘板数据
    pink: '#ff69b4',    // 粉红色，用于一字涨停数据
    darkred: '#8b0000'  // 深红色，用于一字跌停数据
};

// 判断是否在开盘时间
function isMarketOpen(timeStr) {
    if (!timeStr) return false;

    // 解析时间字符串 (HH:MM 格式)
    const timeParts = timeStr.split(':');
    if (timeParts.length !== 2) return false;

    const hour = parseInt(timeParts[0]);
    const minute = parseInt(timeParts[1]);
    const totalMinutes = hour * 60 + minute;

    // 开盘时间段：9:30-11:30 (570-690分钟), 13:00-15:00 (780-900分钟)
    const morningStart = 9 * 60 + 30;  // 570
    const morningEnd = 11 * 60 + 30;   // 690
    const afternoonStart = 13 * 60;    // 780
    const afternoonEnd = 15 * 60;      // 900

    return (totalMinutes >= morningStart && totalMinutes <= morningEnd) ||
           (totalMinutes >= afternoonStart && totalMinutes <= afternoonEnd);
}

// 过滤开盘时间的数据
function filterMarketHoursData(data) {
    if (!Array.isArray(data)) return [];

    return data.filter(item => {
        if (!item.time) return false;
        return isMarketOpen(item.time);
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeCharts();
    loadAllData();
    loadRealtimeHistory(); // 加载当天实时数据历史

    // 设置定时刷新（历史数据每5分钟，实时数据每1分钟）
    setInterval(loadAllData, 5 * 60 * 1000);
    setInterval(loadRealtimeData, 60 * 1000);
});

// 初始化所有图表
function initializeCharts() {
    // 暂时注释掉原来的图表，只使用合并图表
    /*
    // 连板进度图表（如果存在）
    const lianbanElement = document.getElementById('lianbanChart');
    if (lianbanElement) {
        const lianbanCtx = lianbanElement.getContext('2d');
        lianbanChart = new Chart(lianbanCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        bottom: 25
                    }
                },
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                const dataIndex = context.dataIndex;
                                const datasetIndex = context.datasetIndex;
                                const level = context.dataset.label;
    
                                // 获取原始数据
                                if (currentLianbanData && currentLianbanData[dataIndex]) {
                                    const originalValue = currentLianbanData[dataIndex].progress[level]?.percentage || 0;
                                    const successCount = currentLianbanData[dataIndex].progress[level]?.success || 0;
                                    const totalCount = currentLianbanData[dataIndex].progress[level]?.total || 0;
    
                                    return `${level}: ${originalValue}% (${successCount}/${totalCount})`;
                                }
    
                                return `${level}: ${context.parsed.y}%`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        min: 0,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 20,
                            callback: function (value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 9
                            },
                            maxTicksLimit: 15, // 限制X轴标签数量
                            maxRotation: 0,
                            padding: 8
                        },
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    
        // 市场涨跌分布图表
        const marketCtx = document.getElementById('marketChart').getContext('2d');
        marketChart = new Chart(marketCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        bottom: 25
                    }
                },
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        min: 0,
                        max: 4000,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 500
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    
        // 涨跌停统计图表
        const limitCtx = document.getElementById('limitChart').getContext('2d');
        limitChart = new Chart(limitCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        min: 0,
                        max: 80,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 10
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 3,
                        hoverRadius: 5
                    },
                    line: {
                        tension: 0.1
                    }
                }
            }
        });
    
        // 成交金额图表
        const volumeCtx = document.getElementById('volumeChart').getContext('2d');
        volumeChart = new Chart(volumeCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        min: 0,
                        max: 15000,
                        ticks: {
                            font: {
                                size: 10
                            },
                            stepSize: 2500,
                            callback: function (value) {
                                return formatAmount(value);
                            }
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
        */

    // 初始化实时图表
    // initializeRealtimeCharts();

    // 初始化合并图表
    initializeMergedCharts();
}

// 加载所有数据
async function loadAllData() {
    try {
        await Promise.all([
            loadLianbanData(),
            loadMarketData(),  // 加载市场数据
            loadLimitData(),   // 加载涨跌停数据
            loadRealtimeData()  // 优先使用实时数据
        ]);

        updateLastUpdateTime();
    } catch (error) {
        console.error('加载数据失败:', error);
        showError('数据加载失败，请稍后重试');
    }
}

// 加载连板进度数据
async function loadLianbanData() {
    try {
        console.log('🔄 开始加载连板数据...');
        const response = await fetch('/api/lianban_progress');
        const result = await response.json();

        console.log('📡 连板数据API响应:', {
            status: response.status,
            success: result.success,
            dataLength: result.data ? result.data.length : 0,
            stocksKeys: result.latest_stocks ? Object.keys(result.latest_stocks) : null
        });

        if (result.success) {
            console.log('📊 更新连板图表...');
            updateLianbanChart(result.data);

            console.log('🏗️ 更新金字塔...');
            updateModernPyramid(result.data, result.latest_stocks);

            console.log('✅ 连板数据处理完成');
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('❌ 连板数据加载失败:', error);
        // 显示错误信息到金字塔容器
        const pyramidElement = document.getElementById('lianban-pyramid-visual');
        if (pyramidElement) {
            pyramidElement.innerHTML = `<p class="text-danger text-center">数据加载失败: ${error.message}</p>`;
        }
    }
}

// 加载市场数据
async function loadMarketData() {
    try {
        const response = await fetch('/api/market_summary');
        const result = await response.json();

        console.log('市场数据API响应:', result);

        if (result.success) {
            console.log('市场数据加载成功，数据长度:', result.data.length);
            console.log('市场数据样本:', result.data.slice(0, 2));

            // 保存数据到全局变量
            currentMarketData = result.data;
            currentLimitData = result.data; // 涨跌停数据来自市场数据
            currentVolumeData = result.data; // 成交金额数据来自市场数据

            updateMarketChart(result.data);
            updateVolumeChart(result.data);
            updateMarketBars(result.data);
            // 市场数据中也包含涨跌停信息，直接更新
            updateLimitBars(result.data);

            // 更新合并图表
            updateMarketMergedChart();
            updateLimitMergedChart();
            updateVolumeMergedChart();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('市场数据加载失败:', error);
    }
}

// 加载涨跌停数据
async function loadLimitData() {
    try {
        const response = await fetch('/api/limit_stats');
        const result = await response.json();

        if (result.success) {
            updateLimitChart(result.data);
            updateLimitBars(result.data);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('涨跌停数据加载失败:', error);
    }
}



// 更新市场图表 - 已合并到updateMarketMergedChart中
function updateMarketChart(data) {
    if (!data || data.length === 0) return;
    
    // 保存到全局变量
    currentMarketData = data;
    
    // 更新合并图表
    updateMarketMergedChart();
}

// 更新涨跌停图表
function updateLimitChart(data) {
    if (!data || data.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const dates = data.map(item => formatDate(item.date));

    limitChart.data.labels = dates;
    limitChart.data.datasets = [
        {
            label: '涨停数量',
            data: data.map(item => item.limit_up || item.limit_up_total || 0),
            backgroundColor: 'transparent',
            borderColor: colors.danger,
            borderWidth: 2,
            fill: false,
            pointBackgroundColor: colors.danger,
            pointBorderColor: colors.danger,
            pointRadius: 3,
            pointHoverRadius: 5
        },
        {
            label: '跌停数量',
            data: data.map(item => item.limit_down || item.limit_down_total || 0),
            backgroundColor: 'transparent',
            borderColor: colors.success,
            borderWidth: 2,
            fill: false,
            pointBackgroundColor: colors.success,
            pointBorderColor: colors.success,
            pointRadius: 3,
            pointHoverRadius: 5
        }
    ];
    limitChart.update();
}

// 更新成交金额图表
function updateVolumeChart(data) {
    if (!data || data.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const dates = data.map(item => formatDate(item.date));

    volumeChart.data.labels = dates;
    volumeChart.data.datasets = [
        {
            label: '成交金额',
            data: data.map(item => item.volume || item.total_amount || 0),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            fill: true,
            tension: 0.1
        }
    ];
    volumeChart.update();
}

// 工具函数
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
}

function formatAmount(amount) {
    if (amount >= 1e12) {
        return (amount / 1e12).toFixed(1) + '万亿';
    } else if (amount >= 1e8) {
        return (amount / 1e8).toFixed(1) + '亿';
    } else if (amount >= 1e4) {
        return (amount / 1e4).toFixed(1) + '万';
    }
    return amount.toString();
}

function getColorByIndex(index, alpha = 1) {
    const colorList = [
        `rgba(255, 99, 132, ${alpha})`,
        `rgba(54, 162, 235, ${alpha})`,
        `rgba(255, 205, 86, ${alpha})`,
        `rgba(75, 192, 192, ${alpha})`,
        `rgba(153, 102, 255, ${alpha})`,
        `rgba(255, 159, 64, ${alpha})`
    ];
    return colorList[index % colorList.length];
}

// 金字塔级别颜色映射函数
function getPyramidLevelColor(level, alpha = 1) {
    const colorMap = {
        '首板': `rgba(220, 53, 69, ${alpha})`,    // 红色
        '1进2': `rgba(40, 167, 69, ${alpha})`,    // 绿色  
        '2进3': `rgba(0, 123, 255, ${alpha})`,    // 蓝色
        '3进4': `rgba(255, 193, 7, ${alpha})`,    // 黄色
        '4进5': `rgba(111, 66, 193, ${alpha})`,   // 紫色
        '5进6': `rgba(220, 53, 69, ${alpha})`,    // 红色
        '6进7': `rgba(100, 100, 69, ${alpha})`,    // 绿色
        '7进8': `rgba(0, 123, 255, ${alpha})`,    // 蓝色
        '8进9': `rgba(255, 193, 7, ${alpha})`,    // 黄色
        '9进10': `rgba(111, 66, 193, ${alpha})`   // 紫色
    };
    return colorMap[level] || getColorByIndex(0, alpha);
}

function updateLastUpdateTime() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN');
    document.getElementById('last-update').textContent = timeStr;
}

function showError(message) {
    console.error(message);
    // 这里可以添加错误提示UI
}

// 更新连板概览
function updateLianbanSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];
    const progress = latest.progress;

    let html = '<div class="row">';
    Object.keys(progress).forEach(level => {
        const item = progress[level];
        html += `
        <div class="col-4 mb-1">
            <div class="summary-item">
                <div class="summary-value">${item.percentage}%</div>
                <div class="summary-label">${level}</div>
                <small class="text-muted" style="font-size: 0.65rem;">${item.success}/${item.total}</small>
            </div>
        </div>
    `;
    });
    html += '</div>';

    document.getElementById('lianban-summary').innerHTML = html;
}

// 更新市场概览
function updateMarketSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    // 计算总数和比例
    const totalStocks = latest.up_count + latest.down_count + (latest.flat_count || 0);

    // 更新涨跌分布 - 带对比横条
    const marketBars = document.getElementById('market-bars');
    if (marketBars) {
        const upPercent = totalStocks > 0 ? (latest.up_count / totalStocks * 100) : 0;
        const downPercent = totalStocks > 0 ? (latest.down_count / totalStocks * 100) : 0;
        const flatPercent = totalStocks > 0 ? ((latest.flat_count || 0) / totalStocks * 100) : 0;

        marketBars.innerHTML = `
        <div class="market-bar-row">
            <span class="bar-label rise">涨 ${latest.up_count}</span>
            <span class="bar-label fall">跌 ${latest.down_count}</span>
        </div>
        <div class="bar-container">
            <div class="bar-segment rise" style="width: ${upPercent}%"></div>
            <div class="bar-segment flat" style="width: ${flatPercent}%"></div>
            <div class="bar-segment fall" style="width: ${downPercent}%"></div>
        </div>
    `;
    }

    // 更新成交金额
    const volumeInfo = document.getElementById('volume-info');
    if (volumeInfo) {
        volumeInfo.innerHTML = `
        <div class="volume-amount">${latest.total_amount_formatted}</div>
        <div class="volume-label">成交金额</div>
    `;
    }
}

// 更新涨跌停概览
function updateLimitSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    // 更新涨跌停分布 - 带对比横条
    const limitBars = document.getElementById('limit-bars');
    if (limitBars) {
        const totalLimits = latest.limit_up_total + latest.limit_down_total;

        if (totalLimits > 0) {
            const limitUpPercent = (latest.limit_up_total / totalLimits * 100);
            const limitDownPercent = (latest.limit_down_total / totalLimits * 100);

            limitBars.innerHTML = `
            <div class="limit-bar-row">
                <span class="bar-label rise">涨停 ${latest.limit_up_total}</span>
                <span class="bar-label fall">跌停 ${latest.limit_down_total}</span>
            </div>
            <div class="bar-container">
                <div class="bar-segment limit-up" style="width: ${limitUpPercent}%"></div>
                <div class="bar-segment limit-down" style="width: ${limitDownPercent}%"></div>
            </div>
        `;
        } else {
            limitBars.innerHTML = `
            <div class="limit-bar-row">
                <span class="bar-label rise">涨停 ${latest.limit_up_total}</span>
                <span class="bar-label fall">跌停 ${latest.limit_down_total}</span>
            </div>
            <div class="bar-container">
                <div class="bar-segment limit-up" style="width: 50%"></div>
                <div class="bar-segment limit-down" style="width: 50%"></div>
            </div>
        `;
        }
    }
}

// 更新连板进度图表
function updateLianbanChart(data) {
    if (!data || data.length === 0) return;

    currentLianbanData = data; // 保存数据供切换使用
    // 更新合并图表，显示所有级别合并
    updateLianbanMergedChart();
}

// 渲染连板图表
function renderLianbanChart(viewType) {
    if (!currentLianbanData || currentLianbanData.length === 0) return;

    const dates = currentLianbanData.map(item => formatDate(item.date));
    let levels, getColorFunc;

    if (viewType === 'main') {
        // 主要级别：首板到3进4
        levels = ['首板', '1进2', '2进3', '3进4'];
        getColorFunc = getMainLevelColor;
    } else {
        // 高级别：3进4到9进10
        levels = ['3进4', '5进6', '9进10'];
        getColorFunc = getHighLevelColor;
    }

    const datasets = levels.map((level, index) => {
        const rawData = currentLianbanData.map(item => item.progress[level]?.percentage || 0);

        // 对100%的数据点进行处理，避免图表失真
        const processedData = rawData.map(value => {
            if (value === 100) {
                return 95; // 将100%显示为95%，保持图表比例
            }
            return value;
        });

        return {
            label: level,
            data: processedData,
            borderColor: getColorFunc(index),
            backgroundColor: getColorFunc(index, 0.1),
            fill: false,
            tension: 0.3, // 增加平滑度
            borderWidth: 2,
            pointRadius: 2,
            pointHoverRadius: 4
        };
    });

    lianbanChart.data.labels = dates;
    lianbanChart.data.datasets = datasets;
    lianbanChart.update();
}

// 为主要级别定义更清晰的颜色
function getMainLevelColor(index, alpha = 1) {
    const colors = [
        `rgba(220, 53, 69, ${alpha})`,   // 首板 - 红色
        `rgba(40, 167, 69, ${alpha})`,   // 1进2 - 绿色
        `rgba(0, 123, 255, ${alpha})`,   // 2进3 - 蓝色
        `rgba(255, 193, 7, ${alpha})`    // 3进4 - 黄色
    ];
    return colors[index % colors.length];
}

// 为高级别定义颜色
function getHighLevelColor(index, alpha = 1) {
    const colors = [
        `rgba(255, 193, 7, ${alpha})`,   // 3进4 - 黄色
        `rgba(111, 66, 193, ${alpha})`,  // 5进6 - 紫色
        `rgba(255, 99, 132, ${alpha})`   // 9进10 - 粉色
    ];
    return colors[index % colors.length];
}

// 切换连板视图
function switchLianbanView(viewType) {
    currentLianbanView = viewType;

    // 更新按钮状态
    document.getElementById('btn-main-levels').classList.toggle('active', viewType === 'main');
    document.getElementById('btn-high-levels').classList.toggle('active', viewType === 'high');

    // 重新渲染图表
    renderLianbanChart(viewType);
}

// 更新现代金字塔
function updateModernPyramid(progressData, stocksData) {
    console.log('🔍 updateModernPyramid called with:', {
        progressDataLength: progressData ? progressData.length : 0,
        stocksDataKeys: stocksData ? Object.keys(stocksData) : null
    });

    if (!progressData || progressData.length === 0) {
        console.log('❌ No progress data available');
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 获取最新一天的进度数据
    const latestProgress = progressData[progressData.length - 1].progress;

    // 动态获取所有存在的级别（从API数据中）
    const allAvailableLevels = new Set();

    // 从进度数据中获取级别
    Object.keys(latestProgress).forEach(level => {
        if (latestProgress[level] && latestProgress[level].total > 0) {
            allAvailableLevels.add(level);
        }
    });

    // 从股票数据中获取级别
    if (stocksData) {
        Object.keys(stocksData).forEach(level => {
            if (stocksData[level] && stocksData[level].length > 0) {
                allAvailableLevels.add(level);
            }
        });
    }

    // 定义级别排序规则（从高到低）
    const levelOrderFunc = (level) => {
        if (level === '首板') return 0;
        const match = level.match(/(\d+)进(\d+)/);
        if (match) {
            return -parseInt(match[1]); // 负数使得数字越大排序越靠前
        }
        return 1000; // 未知格式排在最后
    };

    // 筛选并排序实际存在数据的级别
    const existingLevels = Array.from(allAvailableLevels)
        .filter(level => {
            const progressInfo = latestProgress[level];
            const stocks = stocksData ? (stocksData[level] || []) : [];
            return progressInfo && (progressInfo.total > 0 || stocks.length > 0);
        })
        .sort((a, b) => levelOrderFunc(a) - levelOrderFunc(b));

    console.log('📊 Available levels:', Array.from(allAvailableLevels));
    console.log('✅ Existing levels:', existingLevels);

    // 显示所有存在的级别，不再限制显示级别数量
    const displayLevels = existingLevels;

    console.log('🎯 Display levels:', displayLevels);

    // 如果没有任何级别有数据，显示提示信息
    if (existingLevels.length === 0) {
        console.log('❌ No existing levels found');
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 更新成功率显示
    updateSuccessRatesDisplay(latestProgress, displayLevels);

    // 动态调整金字塔高度分配
    const containerElement = document.getElementById('lianban-pyramid-visual').parentElement;
    const totalHeight = containerElement.clientHeight - 40; // 减去padding
    const levelCount = displayLevels.length;

    // 计算总股票数量
    const totalStocks = displayLevels.reduce((total, level) => {
        const stocks = stocksData ? (stocksData[level] || []) : [];
        return total + stocks.length;
    }, 0);

    // 计算整体胜率统计
    const overallStats = calculateOverallSuccessRate(latestProgress, displayLevels);

    let html = '<div class="lianban-text-display">';

    // 添加整体胜率显示
    if (overallStats.totalAttempts > 0) {
        html += `
        <div class="overall-success-rate">
            <div class="overall-stats-header">
                <span class="overall-title">整体胜率</span>
                <span class="overall-rate">${overallStats.overallRate}%</span>
            </div>
            <div class="overall-stats-detail">
                成功: ${overallStats.totalSuccess} | 尝试: ${overallStats.totalAttempts} | 存在: ${overallStats.totalCurrent}
            </div>
        </div>
        `;
    }

    // 添加CSS样式
    const styleId = 'lianban-pyramid-styles';
    if (!document.getElementById(styleId)) {
        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = `
            .lianban-level-row {
                margin-bottom: 8px;
                border: 1px solid #eee;
                border-radius: 5px;
                padding: 8px;
                background-color: #fafafa;
            }
            .level-info {
                display: flex;
                align-items: center;
                margin-bottom: 5px;
                cursor: pointer;
            }
            .level-badge {
                padding: 2px 8px;
                border-radius: 4px;
                margin-right: 10px;
                font-weight: bold;
                color: white;
                background-color: #007bff;
            }
            .level-首板 { background-color: #28a745; }
            .level-1-2 { background-color: #17a2b8; }
            .level-2-3 { background-color: #fd7e14; }
            .level-3-4 { background-color: #dc3545; }
            .level-4-5 { background-color: #6f42c1; }
            .level-5-6 { background-color: #6610f2; }
            .level-stats {
                display: flex;
                align-items: center;
                gap: 8px;
                flex-grow: 1;
            }
            .success-rate-text {
                font-weight: bold;
            }
            .stock-count {
                color: #666;
                font-size: 0.9em;
            }
            .stocks-list {
                transition: max-height 0.3s ease, opacity 0.3s ease;
                overflow: hidden;
            }
            .stocks-list.collapsed {
                max-height: 0 !important;
                padding: 0 !important;
                opacity: 0;
                margin: 0;
            }
            .stock-item {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 2px 4px;
                margin: 2px;
                display: inline-block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                cursor: pointer;
                max-width: 100%;
            }
            .stock-item:hover {
                filter: brightness(0.9);
            }
            .more-stocks {
                text-align: center;
                font-weight: bold;
                background-color: #f0f0f0;
                color: #666;
            }
            .toggle-btn {
                background: none;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 0 5px;
                font-size: 14px;
                cursor: pointer;
                margin-left: auto;
            }
            .toggle-btn:hover {
                background-color: #f0f0f0;
            }
        `;
        document.head.appendChild(styleElement);
    }

    displayLevels.forEach((level, index) => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];

        const successRate = progressInfo.percentage;
        const stockCount = stocks.length;
        const successCount = progressInfo.success;
        const totalCount = progressInfo.total;

        // 智能高度分配：根据连板级别优先级调整
        const levelPriority = getLevelPriority(level);
        const calculatedHeight = calculateLevelHeight(level, stockCount, totalHeight, totalStocks, levelPriority);

        // 生成股票显示
        let stocksHtml = '';
        let allStocksTooltip = '';
        let gridCols = 4; // 默认4列
        let fontSize = '0.7rem'; // 默认字体大小

        if (stocks.length > 0) {
            // 按状态排序，成功的在前面
            const sortedStocks = stocks.sort((a, b) => {
                if (a.status === '成' && b.status !== '成') return -1;
                if (a.status !== '成' && b.status === '成') return 1;
                return parseFloat(b.change_percent) - parseFloat(a.change_percent);
            });

            // 智能布局算法：根据连板级别和股票数量动态调整显示策略
            const layoutResult = calculateOptimalLayout(sortedStocks, calculatedHeight, level);
            gridCols = layoutResult.gridCols;
            fontSize = layoutResult.fontSize;
            const { displayStocks, hasMore } = layoutResult;

            stocksHtml = displayStocks.map(stock => {
                const isSuccess = stock.status === '成';
                const isFailed = stock.status === '炸';
                const changePercent = parseFloat(stock.change_percent);
                let colorClass = 'text-success'; // 默认失败绿色
                if (isSuccess) {
                    colorClass = 'text-danger'; // 成功红色
                } else if (isFailed) {
                    colorClass = 'text-warning'; // 炸板黄色
                }
                const sign = changePercent >= 0 ? '+' : '';
                const statusText = isSuccess ? '成功' : (isFailed ? '炸板' : '失败');
                return `<span class="stock-item ${colorClass}" title="${stock.name} ${sign}${changePercent.toFixed(2)}% (${statusText})" onclick="showStockDetail('${stock.name}', '${stock.code}', '${stock.change_percent}', '${stock.industry || ''}', '${stock.status}')">${stock.name} ${sign}${changePercent.toFixed(2)}%</span>`;
            }).join('');

            // 如果有更多股票，添加"查看更多"按钮
            if (hasMore) {
                const remainingCount = sortedStocks.length - displayStocks.length;
                stocksHtml += `<span class="stock-item more-stocks" onclick="showAllStocks('${level}', ${JSON.stringify(sortedStocks).replace(/"/g, '&quot;')})">+${remainingCount}更多</span>`;
            }

            // 生成所有股票的提示信息
            allStocksTooltip = sortedStocks.map(stock => {
                const changePercent = parseFloat(stock.change_percent);
                const sign = changePercent >= 0 ? '+' : '';
                const isSuccess = stock.status === '成';
                const isFailed = stock.status === '炸';
                const statusText = isSuccess ? '成功' : (isFailed ? '炸板' : '失败');
                return `${stock.name} ${sign}${changePercent.toFixed(2)}% (${statusText})`;
            }).join('\\n');
        }

        // 添加折叠/展开功能
        const collapsibleId = `stocks-list-${level.replace(/[^a-zA-Z0-9]/g, '')}`;
        const hasStocks = stocks.length > 0;
        const isFirstLevel = index === 0; // 首板或最高级别默认展开
        
        html += `
        <div class="lianban-level-row" style="min-height: ${Math.max(60, calculatedHeight * 0.5)}px;">
            <div class="level-info">
                <span class="level-badge level-${level.replace('进', '-')}">${level}</span>
                <span class="level-stats" onclick="showLevelDetail('${level}', ${successRate}, ${successCount}, ${totalCount}, ${stockCount})">
                    <span class="success-rate-text">${successRate}%</span>
                    <span class="success-count">${successCount}/${totalCount}</span>
                    <span class="stock-count">(${stockCount}只)</span>
                </span>
                ${hasStocks ? `<button class="toggle-btn" onclick="document.getElementById('${collapsibleId}').classList.toggle('collapsed'); this.textContent = this.textContent === '−' ? '+' : '−';">${isFirstLevel ? '−' : '+'}</button>` : ''}
            </div>
            ${hasStocks ? `
            <div id="${collapsibleId}" class="stocks-list ${isFirstLevel ? '' : 'collapsed'}" style="display: grid; grid-template-columns: repeat(${gridCols || 4}, 1fr); gap: 4px; padding: 5px;">
                ${stocksHtml}
            </div>
            ` : ''}
        </div>
        `;
    });

    html += '</div>';

    document.getElementById('lianban-pyramid-visual').innerHTML = html;

    // 更新日期显示
    const today = new Date().toLocaleDateString('zh-CN');
    document.getElementById('pyramid-date').textContent = today;
}

// 更新成功率显示
function updateSuccessRatesDisplay(latestProgress, levelOrder) {
    levelOrder.forEach(level => {
        const progressInfo = latestProgress[level];
        if (!progressInfo) return;

        const successRate = progressInfo.percentage || 0;

        const rateElement = document.querySelector(`.success-rate-item[data-level="${level}"] .rate-value`);
        if (rateElement) {
            rateElement.textContent = successRate > 0 ? `${successRate}%` : '-';
        }
    });
}



// 计算整体胜率统计
function calculateOverallSuccessRate(latestProgress, displayLevels) {
    let totalSuccess = 0;
    let totalAttempts = 0;
    let totalCurrent = 0;

    displayLevels.forEach(level => {
        const progressInfo = latestProgress[level];
        if (progressInfo) {
            totalSuccess += progressInfo.success || 0;
            totalAttempts += progressInfo.total || 0;
        }
    });

    const overallRate = totalAttempts > 0 ? Math.round((totalSuccess / totalAttempts) * 100) : 0;

    return {
        totalSuccess,
        totalAttempts,
        totalCurrent,
        overallRate
    };
}

// 智能布局算法：根据连板级别和股票数量动态调整显示策略
function calculateOptimalLayout(stocks, containerHeight, level = '') {
    const stockCount = stocks.length;
    const baseRowHeight = 18; // 减少每行基础高度
    const minContainerHeight = 60; // 最小容器高度
    const safeContainerHeight = Math.max(containerHeight, minContainerHeight);
    const maxRows = Math.max(2, Math.floor((safeContainerHeight - 10) / baseRowHeight)); // 确保至少2行

    let gridCols = 4; // 默认4列
    let fontSize = '0.7rem'; // 默认字体大小
    let maxDisplayStocks = maxRows * gridCols;

    // 判断连板级别优先级
    const levelPriority = getLevelPriority(level);

    console.log(`🎯 计算布局: ${level} (优先级: ${levelPriority}, 股票数: ${stockCount}, 容器高度: ${containerHeight} -> ${safeContainerHeight}, 最大行数: ${maxRows})`);

    // 首板特殊处理：确保显示足够多的股票
    if (level === '首板') {
        console.log(`🚨 首板特殊处理: 确保显示足够多的股票`);

        // 首板股票通常很多，需要显示更多
        if (stockCount <= 16) {
            gridCols = 8;
            fontSize = '0.65rem';
            maxDisplayStocks = stockCount; // 少量时全部显示
        } else if (stockCount <= 40) {
            gridCols = 8;
            fontSize = '0.6rem';
            maxDisplayStocks = Math.min(stockCount, 32); // 确保至少显示32个
        } else if (stockCount <= 80) {
            gridCols = 10;
            fontSize = '0.55rem';
            maxDisplayStocks = Math.min(stockCount, 50); // 确保至少显示50个
        } else {
            gridCols = 12;
            fontSize = '0.5rem';
            maxDisplayStocks = Math.min(stockCount, 72); // 确保至少显示72个
        }
    }
    // 1进2特殊处理：确保显示足够的股票
    else if (level === '1进2') {
        console.log(`🚨 1进2特殊处理: 确保显示足够的股票`);

        if (stockCount <= 14) {
            gridCols = 7;
            fontSize = '0.65rem';
            maxDisplayStocks = stockCount;
        } else if (stockCount <= 28) {
            gridCols = 7;
            fontSize = '0.6rem';
            maxDisplayStocks = Math.min(stockCount, 28); // 确保至少显示28个
        } else {
            gridCols = 8;
            fontSize = '0.55rem';
            maxDisplayStocks = Math.min(stockCount, 40); // 确保至少显示40个
        }
    }
    // 高级别连板（3进4及以上）：优先完整显示
    else if (levelPriority >= 3) {
        console.log(`🔥 高级别连板 ${level}: 完整显示所有股票`);

        // 高级别连板通常股票较少，优先保证完整显示
        if (stockCount <= 4) {
            gridCols = 4;
            fontSize = '0.75rem';
            maxDisplayStocks = stockCount; // 显示所有股票
        } else if (stockCount <= 8) {
            gridCols = 4;
            fontSize = '0.7rem';
            maxDisplayStocks = stockCount; // 显示所有股票
        } else if (stockCount <= 12) {
            gridCols = 6;
            fontSize = '0.65rem';
            maxDisplayStocks = stockCount; // 显示所有股票
        } else {
            // 即使是高级别，股票过多时也需要适当限制
            gridCols = 6;
            fontSize = '0.6rem';
            maxDisplayStocks = Math.min(stockCount, Math.max(18, maxRows * 6));
        }
    }
    // 中级别连板（2进3）：适度显示
    else if (levelPriority === 2) {
        console.log(`📈 中级别连板 ${level}: 适度显示策略`);

        if (stockCount <= 12) {
            gridCols = 6;
            fontSize = '0.7rem';
            maxDisplayStocks = stockCount; // 中等数量完整显示
        } else if (stockCount <= 24) {
            gridCols = 6;
            fontSize = '0.65rem';
            maxDisplayStocks = Math.min(stockCount, Math.max(18, maxRows * 6)); // 至少显示18个
        } else {
            gridCols = 8;
            fontSize = '0.6rem';
            maxDisplayStocks = Math.min(stockCount, Math.max(24, maxRows * 8)); // 至少显示24个
        }
    }

    // 最终安全检查：确保至少显示一些股票
    if (maxDisplayStocks < 4 && stockCount > 0) {
        maxDisplayStocks = Math.min(stockCount, 4);
        gridCols = Math.min(gridCols, stockCount);
    }

    // 确保不超过合理的容器限制，但不要过于严格
    const containerLimit = Math.max(12, maxRows * gridCols); // 至少显示12个股票
    maxDisplayStocks = Math.min(maxDisplayStocks, containerLimit);

    const displayStocks = stocks.slice(0, maxDisplayStocks);
    const hasMore = stockCount > maxDisplayStocks;

    console.log(`✅ 布局结果: 显示${displayStocks.length}/${stockCount}个股票, ${gridCols}列, ${fontSize}字体${hasMore ? ', 有更多' : ''}`);

    return {
        displayStocks,
        hasMore,
        gridCols,
        fontSize
    };
}

// 获取连板级别优先级（数字越大优先级越高）
function getLevelPriority(level) {
    if (!level) return 0;

    if (level === '首板') return 0;
    if (level === '1进2') return 1;
    if (level === '2进3') return 2;

    // 解析"X进Y"格式
    const match = level.match(/(\d+)进(\d+)/);
    if (match) {
        const fromLevel = parseInt(match[1]);
        return fromLevel; // 3进4返回3，4进5返回4，以此类推
    }

    return 0; // 未知格式默认低优先级
}

function calculateLevelHeight(level, stockCount, totalHeight, totalStocks, levelPriority) {
    // 基础参数设置
    const minHeight = 80; // 最小高度，确保内容可见
    const maxHeight = 350; // 降低最大高度，防止单个层级过高
    const heightPerStock = 30; // 每个股票的基础高度（稍微减小）
    const padding = 20; // 内边距（稍微减小）

    // 根据级别优先级调整每行显示的股票数
    let stocksPerRow;
    if (level === '首板') {
        stocksPerRow = 8; // 首板每行显示更多股票
    } else if (level === '1进2') {
        stocksPerRow = 7; // 1进2每行显示较多股票
    } else if (levelPriority >= 3) {
        stocksPerRow = 4; // 高级别连板每行显示较少股票
    } else {
        stocksPerRow = 6; // 默认每行6个股票
    }

    // 确保每个级别至少显示一定数量的股票
    let minDisplayRows;
    if (level === '首板') {
        minDisplayRows = 2; // 首板至少显示2行
    } else if (level === '1进2') {
        minDisplayRows = 1.5; // 1进2至少显示1.5行
    } else if (levelPriority >= 3) {
        minDisplayRows = 1; // 高级别至少显示1行
    } else {
        minDisplayRows = 1; // 其他级别至少显示1行
    }

    // 计算实际需要的行数
    const actualRows = Math.ceil(stockCount / stocksPerRow);
    
    // 确保至少显示最小行数
    const numRows = Math.max(minDisplayRows, Math.min(actualRows, 8)); // 最多显示8行

    // 计算高度
    let height = numRows * heightPerStock + padding;

    // 应用最小和最大高度限制
    height = Math.max(minHeight, height);
    height = Math.min(maxHeight, height);

    // 如果没有股票，则返回一个较小的高度，但确保所有级别都有一定显示空间
    if (stockCount === 0) {
        if (level === '首板') {
            return 70; // 首板即使没有股票也保留一定空间
        }
        return 60;
    }

    console.log(`📏 ${level} 高度计算: ${stockCount}只股票, ${numRows}行, 最终高度${height}px`);
    return height;
}

// 显示所有股票的弹窗
function showAllStocks(level, stocks) {
    try {
        const stocksArray = typeof stocks === 'string' ? JSON.parse(stocks) : stocks;

        let stocksHtml = stocksArray.map(stock => {
            const isSuccess = stock.status === '成';
            const isFailed = stock.status === '炸';
            const changePercent = parseFloat(stock.change_percent);
            let colorClass = isSuccess ? 'text-danger' : (isFailed ? 'text-warning' : 'text-success');
            const sign = changePercent >= 0 ? '+' : '';
            const statusText = isSuccess ? '成功' : (isFailed ? '炸板' : '失败');

            return `
                <div class="stock-detail-item" style="margin: 5px 0; padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                    <span class="${colorClass}" style="font-weight: bold;">${stock.name}</span>
                    <span style="margin-left: 10px;">${sign}${changePercent.toFixed(2)}%</span>
                    <span style="margin-left: 10px; font-size: 0.8em; color: #666;">(${statusText})</span>
                    ${stock.industry ? `<span style="margin-left: 10px; font-size: 0.8em; color: #999;">${stock.industry}</span>` : ''}
                </div>
            `;
        }).join('');

        const modalHtml = `
            <div class="modal fade" id="allStocksModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${level} 全部股票 (${stocksArray.length}只)</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" style="max-height: 400px; overflow-y: auto;">
                            ${stocksHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('allStocksModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modalElement = document.getElementById('allStocksModal');
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else {
            // 如果Bootstrap不可用，使用简单的显示方式
            modalElement.style.display = 'block';
            modalElement.classList.add('show');
            modalElement.style.backgroundColor = 'rgba(0,0,0,0.5)';
        }

    } catch (error) {
        console.error('显示所有股票时出错:', error);
        alert('显示股票详情时出错，请稍后重试');
    }
}

// 显示级别详情
function showLevelDetail(level, successRate, successCount, totalCount, stockCount) {
    const message = `${level}连板详情：

成功率: ${successRate}%
成功数量: ${successCount}只
尝试数量: ${totalCount}只
当前存在: ${stockCount}只股票

${successRate >= 50 ? '✅ 成功率较高，市场情绪良好' : '⚠️ 成功率偏低，需谨慎操作'}`;

    if (typeof showToast !== 'undefined') {
        const toastType = successRate >= 50 ? 'success' : 'warning';
        showToast(`${level}: ${successRate}% (${successCount}/${totalCount})`, toastType);
    } else {
        alert(message);
    }
}

// 显示股票详情
function showStockDetail(name, code, changePercent, industry, status) {
    const statusText = status === '成' ? '成功' : '失败';
    const statusColor = status === '成' ? '#28a745' : '#dc3545';

    const message = `
    <div style="text-align: left;">
        <h6 style="color: ${statusColor}; margin-bottom: 10px;">
            ${name} (${code})
        </h6>
        <p style="margin: 5px 0;"><strong>涨幅:</strong> ${changePercent}%</p>
        <p style="margin: 5px 0;"><strong>行业:</strong> ${industry}</p>
        <p style="margin: 5px 0;"><strong>状态:</strong> <span style="color: ${statusColor};">${statusText}</span></p>
    </div>
`;

    // 使用简单的alert，也可以替换为更美观的模态框
    if (typeof showToast !== 'undefined') {
        showToast(`${name}: ${changePercent}% (${statusText})`, status === '成' ? 'success' : 'danger');
    } else {
        alert(`${name} (${code})\n涨幅: ${changePercent}%\n行业: ${industry}\n状态: ${statusText}`);
    }
}

// 更新市场数据条
function updateMarketBars(data) {
    if (!data || data.length === 0) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">暂无数据</div>';
        document.getElementById('volume-info').innerHTML = '<div class="text-center text-muted">暂无数据</div>';
        return;
    }

    const latest = data[data.length - 1];
    if (!latest) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">数据格式错误</div>';
        return;
    }

    const riseCount = latest.rise_count || 0;
    const fallCount = latest.fall_count || 0;
    const flatCount = latest.flat_count || 0;
    const total = riseCount + fallCount + flatCount;

    if (total === 0) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">暂无交易数据</div>';
        return;
    }

    const risePercent = (riseCount / total * 100).toFixed(1);
    const fallPercent = (fallCount / total * 100).toFixed(1);
    const flatPercent = (flatCount / total * 100).toFixed(1);

    const marketBarsHtml = `
    <span class="bar-label-left rise">涨 ${riseCount}</span>
    <div class="bar-container-horizontal">
        <div class="bar-segment-horizontal rise" style="width: ${risePercent}%"></div>
        <div class="bar-segment-horizontal flat" style="width: ${flatPercent}%"></div>
        <div class="bar-segment-horizontal fall" style="width: ${fallPercent}%"></div>
    </div>
    <span class="bar-label-right fall">跌 ${fallCount}</span>
`;

    document.getElementById('market-bars').innerHTML = marketBarsHtml;

    // 更新大盘标题背景色
    updateMarketTitleColor(riseCount, fallCount, flatCount);

    // 更新成交金额
    const volume = latest.volume || 0;
    const volumeHtml = `
    <div class="volume-value">${latest.volume_formatted || formatAmount(volume)}</div>
    <div class="volume-label">成交金额</div>
`;
    document.getElementById('volume-info').innerHTML = volumeHtml;
}

// 更新大盘标题背景色
function updateMarketTitleColor(riseCount, fallCount, flatCount) {
    const marketTitle = document.querySelector('.market-title');
    if (!marketTitle) return;

    // 移除所有状态类
    marketTitle.classList.remove('market-up', 'market-down', 'market-flat');

    // 计算涨跌比例
    const total = riseCount + fallCount + flatCount;
    if (total === 0) {
        // 没有数据时保持默认样式
        return;
    }

    const risePercent = (riseCount / total) * 100;
    const fallPercent = (fallCount / total) * 100;
    const flatPercent = (flatCount / total) * 100;

    // 判断市场状态
    if (risePercent > fallPercent + 5) {
        // 上涨股票明显多于下跌股票（差距超过5%），显示红色
        marketTitle.classList.add('market-up');
    } else if (fallPercent > risePercent + 5) {
        // 下跌股票明显多于上涨股票（差距超过5%），显示绿色
        marketTitle.classList.add('market-down');
    } else {
        // 涨跌相当或平盘较多，显示灰色
        marketTitle.classList.add('market-flat');
    }

    console.log('🎨 大盘状态更新:', {
        riseCount,
        fallCount,
        flatCount,
        risePercent: risePercent.toFixed(1) + '%',
        fallPercent: fallPercent.toFixed(1) + '%',
        flatPercent: flatPercent.toFixed(1) + '%',
        status: marketTitle.classList.contains('market-up') ? '上涨' :
                marketTitle.classList.contains('market-down') ? '下跌' : '横盘'
    });
}

// 更新涨跌停数据条
function updateLimitBars(data) {
    if (!data || data.length === 0) {
        document.getElementById('limit-bars').innerHTML = '<div class="text-center text-muted" style="font-size: 0.7rem;">暂无数据</div>';
        return;
    }

    const latest = data[data.length - 1];
    if (!latest) {
        document.getElementById('limit-bars').innerHTML = '<div class="text-center text-muted" style="font-size: 0.7rem;">数据格式错误</div>';
        return;
    }

    const limitUp = latest.limit_up || latest.limit_up_total || 0;
    const limitDown = latest.limit_down || latest.limit_down_total || 0;
    const total = limitUp + limitDown;

    let limitUpPercent = 50;
    let limitDownPercent = 50;

    if (total > 0) {
        limitUpPercent = (limitUp / total * 100);
        limitDownPercent = (limitDown / total * 100);
    }

    const limitBarsHtml = `
    <span class="bar-label-left rise">涨停 ${limitUp}</span>
    <div class="bar-container-horizontal">
        <div class="bar-segment-horizontal limit-up" style="width: ${limitUpPercent}%"></div>
        <div class="bar-segment-horizontal limit-down" style="width: ${limitDownPercent}%"></div>
    </div>
    <span class="bar-label-right fall">跌停 ${limitDown}</span>
`;

    document.getElementById('limit-bars').innerHTML = limitBarsHtml;
}

// 初始化实时图表
function initializeRealtimeCharts() {
    // 连板进度实时图表
    const lianbanRealtimeCtx = document.getElementById('lianbanRealtimeChart').getContext('2d');
    lianbanRealtimeChart = new Chart(lianbanRealtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 100,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 20,
                        callback: function (value) { return value + '%'; }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 市场涨跌实时图表
    const marketRealtimeCtx = document.getElementById('marketRealtimeChart').getContext('2d');
    marketRealtimeChart = new Chart(marketRealtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 4000,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 500
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 涨跌停实时图表
    const limitRealtimeCtx = document.getElementById('limitRealtimeChart').getContext('2d');
    limitRealtimeChart = new Chart(limitRealtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 80,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 10
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            },
            elements: {
                point: {
                    radius: 2,
                    hoverRadius: 4
                },
                line: {
                    tension: 0.1
                }
            }
        }
    });

    // 成交金额实时图表
    const volumeRealtimeCtx = document.getElementById('volumeRealtimeChart').getContext('2d');
    volumeRealtimeChart = new Chart(volumeRealtimeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 8 },
                        padding: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 15000,
                    ticks: {
                        font: { size: 8 },
                        stepSize: 2500,
                        callback: function (value) { return formatAmount(value); }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 8 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });
}

// 初始化合并图表
function initializeMergedCharts() {
    console.log('开始初始化合并图表');

    // 连板进度合并图表
    const lianbanMergedElement = document.getElementById('lianbanMergedChart');
    console.log('连板合并图表元素:', lianbanMergedElement);

    if (!lianbanMergedElement) {
        console.error('找不到连板合并图表元素');
        return;
    }

    const lianbanMergedCtx = lianbanMergedElement.getContext('2d');
    window.lianbanMergedChart = lianbanMergedChart = new Chart(lianbanMergedCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 100,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 20,
                        callback: function (value) { return value + '%'; }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    console.log('连板合并图表创建成功:', lianbanMergedChart);

    // 市场涨跌合并图表
    const marketMergedCtx = document.getElementById('marketMergedChart').getContext('2d');
    window.marketMergedChart = marketMergedChart = new Chart(marketMergedCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 5000,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 500
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });

    // 涨跌停合并图表
    const limitMergedCtx = document.getElementById('limitMergedChart').getContext('2d');
    window.limitMergedChart = limitMergedChart = new Chart(limitMergedCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 120,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 20
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            },
            elements: {
                point: {
                    radius: 3,
                    hoverRadius: 5
                },
                line: {
                    tension: 0.1
                }
            }
        }
    });

    // 成交金额合并图表
    const volumeMergedCtx = document.getElementById('volumeMergedChart').getContext('2d');
    window.volumeMergedChart = volumeMergedChart = new Chart(volumeMergedCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    bottom: 25
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 8,
                        font: { size: 10 },
                        padding: 10
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    min: 0,
                    max: 25000,
                    ticks: {
                        font: { size: 10 },
                        stepSize: 5000,
                        callback: function (value) {
                            return formatAmount(value);
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: { size: 9 },
                        maxRotation: 0,
                        padding: 8
                    }
                }
            }
        }
    });
}

// 验证连板数据是否有效
function isValidLianbanData(data) {
    if (!data || typeof data !== 'object') return false;

    // 检查是否有连板进度数据
    if (!data.lianban_progress || typeof data.lianban_progress !== 'object') return false;

    // 检查是否至少有一个级别的数据
    const levels = ['首板', '1进2', '2进3', '3进4', '4进5'];
    const hasValidLevel = levels.some(level =>
        data.lianban_progress[level] &&
        typeof data.lianban_progress[level] === 'string' &&
        data.lianban_progress[level].includes('/')
    );

    return hasValidLevel;
}

// 验证市场数据是否有效
function isValidMarketData(data) {
    if (!data || typeof data !== 'object') return false;

    // 检查必要的数字字段 - 更宽松的验证
    const requiredFields = ['up_count', 'down_count'];
    return requiredFields.some(field =>
        data.hasOwnProperty(field) &&
        typeof data[field] === 'number' &&
        !isNaN(data[field]) &&
        data[field] >= 0
    );
}

// 加载当天实时数据历史
async function loadRealtimeHistory() {
    try {
        // 加载连板实时数据历史
        const lianbanResponse = await fetch('/api/realtime/history/lianban');
        const lianbanResult = await lianbanResponse.json();
        if (lianbanResult.success && lianbanResult.data.length > 0) {
            // 过滤掉集合竞价时间段(9:15-9:30)的数据，只显示9:30之后的实时数据
            realtimeDataCache.lianban = filterMarketHoursData(lianbanResult.data);
            console.log('连板实时数据数量(过滤后):', realtimeDataCache.lianban.length);
            console.log('连板实时数据数量(原始):', lianbanResult.data.length);
            updateRealtimeLianbanChart();
        }

        // 加载市场实时数据历史
        const marketResponse = await fetch('/api/realtime/history/market');
        const marketResult = await marketResponse.json();
        if (marketResult.success && marketResult.data.length > 0) {
            // 过滤掉集合竞价时间段(9:15-9:30)的数据，只显示9:30之后的实时数据
            realtimeDataCache.market = filterMarketHoursData(marketResult.data);
            console.log('市场实时数据数量(过滤后):', realtimeDataCache.market.length);
            console.log('市场实时数据数量(原始):', marketResult.data.length);
            console.log('市场实时数据样本:', realtimeDataCache.market.slice(0, 2));
        }

        console.log(`加载实时数据历史: 连板${realtimeDataCache.lianban.length}条, 市场${realtimeDataCache.market.length}条`);

        // 更新合并图表 - 这是关键，确保实时数据能显示
        if (realtimeDataCache.market.length > 0 || realtimeDataCache.lianban.length > 0) {
            console.log('更新合并图表以显示实时数据历史');
            updateMergedCharts();
        }
    } catch (error) {
        console.error('加载实时数据历史失败:', error);
    }
}

// 加载实时数据
async function loadRealtimeData() {
    try {
        await Promise.all([
            loadRealtimeLianbanData(),
            loadRealtimeMarketData()
        ]);
        updateRealtimeIndicators();
    } catch (error) {
        console.error('实时数据加载失败:', error);
    }
}

// 加载实时连板数据
async function loadRealtimeLianbanData() {
    try {
        const response = await fetch('/api/realtime/lianban_progress');
        const result = await response.json();

        if (result.success && isValidLianbanData(result.data)) {
            const currentTime = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // 检查是否在开盘时间
            if (!isMarketOpen(currentTime)) {
                console.log('非开盘时间，跳过连板实时数据缓存:', currentTime);
                return;
            }

            // 检查是否已存在相同时间的数据，避免重复
            const existingIndex = realtimeDataCache.lianban.findIndex(item => item.time === currentTime);
            if (existingIndex >= 0) {
                // 更新现有数据
                realtimeDataCache.lianban[existingIndex] = {
                    time: currentTime,
                    data: result.data
                };
            } else {
                // 添加新数据到缓存
                realtimeDataCache.lianban.push({
                    time: currentTime,
                    data: result.data
                });

                // 保持最近100个数据点
                if (realtimeDataCache.lianban.length > 100) {
                    realtimeDataCache.lianban.shift();
                }
            }

            updateRealtimeLianbanChart();
            updateLianbanMergedChart();
        } else if (result.success) {
            console.warn('连板数据无效，保持之前的数据:', result.data);
        }
    } catch (error) {
        console.error('实时连板数据加载失败:', error);
    }
}

// 加载实时市场数据
async function loadRealtimeMarketData() {
    try {
        const response = await fetch('/api/realtime/market_summary');
        const result = await response.json();

        console.log('实时市场数据API响应:', result);

        if (result.success && isValidMarketData(result.data)) {
            console.log('实时市场数据验证通过:', result.data);

            const currentTime = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // 检查是否在开盘时间
            if (!isMarketOpen(currentTime)) {
                console.log('非开盘时间，跳过实时数据缓存:', currentTime);
                return;
            }

            // 检查是否已存在相同时间的数据，避免重复
            const existingIndex = realtimeDataCache.market.findIndex(item => item.time === currentTime);
            if (existingIndex >= 0) {
                // 更新现有数据
                realtimeDataCache.market[existingIndex] = {
                    time: currentTime,
                    data: result.data
                };
                console.log('更新现有实时数据');
            } else {
                // 添加新数据到缓存
                realtimeDataCache.market.push({
                    time: currentTime,
                    data: result.data
                });
                console.log('添加新实时数据，当前缓存长度:', realtimeDataCache.market.length);

                // 保持最近100个数据点
                if (realtimeDataCache.market.length > 100) {
                    realtimeDataCache.market.shift();
                }
            }

            updateRealtimeMarketChart();
            updateRealtimeLimitChart();
            updateRealtimeVolumeChart();

            // 更新合并图表
            updateMarketMergedChart();
            updateLimitMergedChart();
            updateVolumeMergedChart();
        } else if (result.success) {
            console.warn('市场数据无效，保持之前的数据:', result.data);
            console.warn('数据验证结果:', isValidMarketData(result.data));
        } else {
            console.error('实时市场数据API返回错误:', result.error);
        }
    } catch (error) {
        console.error('实时市场数据加载失败:', error);
    }
}

// 更新实时连板图表
function updateRealtimeLianbanChart() {
    if (realtimeDataCache.lianban.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const labels = realtimeDataCache.lianban.map(item => item.time);
    const levels = currentLianbanView === 'main' ? ['首板', '2进3', '3进4'] : ['3进4', '5进6', '9进10'];

    const datasets = levels.map((level, index) => {
        const data = realtimeDataCache.lianban.map(item => {
            const progress = item.data.lianban_progress;
            if (progress && progress[level]) {
                const match = progress[level].match(/(\d+)\/(\d+)=(\d+)%/);
                return match ? parseInt(match[3]) : 0;
            }
            return 0;
        });

        return {
            label: level,
            data: data,
            borderColor: getMainLevelColor(index),
            backgroundColor: getMainLevelColor(index) + '20',
            fill: false,
            tension: 0.1
        };
    });

    lianbanRealtimeChart.data.labels = labels;
    lianbanRealtimeChart.data.datasets = datasets;
    lianbanRealtimeChart.update();
}

// 更新实时市场图表
function updateRealtimeMarketChart() {
    if (realtimeDataCache.market.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const labels = realtimeDataCache.market.map(item => item.time);
    const upData = realtimeDataCache.market.map(item => item.data.up_count || 0);
    const downData = realtimeDataCache.market.map(item => item.data.down_count || 0);

    const datasets = [
        {
            label: '上涨',
            data: upData,
            borderColor: colors.success,
            backgroundColor: colors.success + '20',
            fill: false
        },
        {
            label: '下跌',
            data: downData,
            borderColor: colors.danger,
            backgroundColor: colors.danger + '20',
            fill: false
        }
    ];

    marketRealtimeChart.data.labels = labels;
    marketRealtimeChart.data.datasets = datasets;
    marketRealtimeChart.update();
}

// 更新实时涨跌停图表
function updateRealtimeLimitChart() {
    if (realtimeDataCache.market.length === 0) return;

    // 暂时禁用原图表更新
    return;

    const labels = realtimeDataCache.market.map(item => item.time);
    const limitUpData = realtimeDataCache.market.map(item => item.data.limit_up_count || 0);
    const limitDownData = realtimeDataCache.market.map(item => item.data.limit_down_count || 0);

    const datasets = [
        {
            label: '涨停',
            data: limitUpData,
            backgroundColor: 'transparent',
            borderColor: colors.danger,
            borderWidth: 2,
            fill: false,
            pointBackgroundColor: colors.danger,
            pointBorderColor: colors.danger,
            pointRadius: 2,
            pointHoverRadius: 4
        },
        {
            label: '跌停',
            data: limitDownData,
            backgroundColor: 'transparent',
            borderColor: colors.success,
            borderWidth: 2,
            fill: false,
            pointBackgroundColor: colors.success,
            pointBorderColor: colors.success,
            pointRadius: 2,
            pointHoverRadius: 4
        }
    ];

    limitRealtimeChart.data.labels = labels;
    limitRealtimeChart.data.datasets = datasets;
    limitRealtimeChart.update();
}

// 更新实时成交金额图表
function updateRealtimeVolumeChart() {
    if (realtimeDataCache.market.length === 0) return;

    const labels = realtimeDataCache.market.map(item => item.time);
    const volumeData = realtimeDataCache.market.map(item => {
        const volume = item.data.volume;
        if (typeof volume === 'string') {
            // 解析类似 "1.2万亿" 的格式
            if (volume.includes('万亿')) {
                return parseFloat(volume.replace('万亿', '')) * 10000;
            } else if (volume.includes('亿')) {
                return parseFloat(volume.replace('亿', ''));
            } else if (volume.includes('万')) {
                return parseFloat(volume.replace('万', '')) / 10000; // 转换为亿
            }
        }
        // 如果是数字，假设单位是亿
        return parseFloat(volume) || 0;
    });

    const datasets = [{
        label: '成交金额(亿)',
        data: volumeData,
        borderColor: colors.primary,
        backgroundColor: colors.primary + '20',
        fill: true,
        tension: 0.1
    }];

    volumeRealtimeChart.data.labels = labels;
    volumeRealtimeChart.data.datasets = datasets;
    volumeRealtimeChart.update();
}

// 更新实时指示器
function updateRealtimeIndicators() {
    const indicators = ['lianban', 'market', 'limit', 'volume'];
    indicators.forEach(type => {
        const indicator = document.getElementById(`${type}-realtime-indicator`);
        if (indicator) {
            indicator.style.color = '#28a745';
            setTimeout(() => {
                indicator.style.color = '#6c757d';
            }, 1000);
        }
    });
}

// 更新合并图表
function updateMergedCharts() {
    updateLianbanMergedChart();
    updateMarketMergedChart();
    updateLimitMergedChart();
    updateVolumeMergedChart();
}

// 更新连板进度合并图表
function updateLianbanMergedChart() {
    if (!lianbanMergedChart) {
        console.log('连板合并图表对象不存在');
        return;
    }

    console.log('开始更新连板合并图表');
    console.log('currentLianbanData长度:', currentLianbanData ? currentLianbanData.length : 0);
    console.log('realtimeDataCache.lianban.length:', realtimeDataCache.lianban.length);

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentLianbanData && currentLianbanData.length > 0) {
        // 历史数据标签（日期）
        historyLabels = currentLianbanData.map(item => formatDate(item.date));

        // 显示所有级别合并，不分主要和高级别
        const allLevels = ['首板', '1进2', '2进3', '3进4', '4进5', '5进6', '6进7', '7进8', '8进9', '9进10'];
        
        // 只显示有数据的级别
        const levels = allLevels.filter(level => 
            currentLianbanData.some(item => item.progress[level] && item.progress[level].total > 0)
        );

        historyDatasets = levels.map((level, index) => {
            const rawData = currentLianbanData.map(item => item.progress[level]?.percentage || 0);

            // 对100%的数据点进行处理，避免图表失真
            const processedData = rawData.map(value => {
                if (value === 100) {
                    return Math.random() * 5 + 95; // 95-100之间的随机值
                }
                return value;
            });

            return {
                label: level,
                data: processedData,
                borderColor: getPyramidLevelColor(level),
                backgroundColor: getPyramidLevelColor(level, 0.2),
                fill: false,
                tension: 0.1
            };
        });

        console.log('历史数据处理完成，数据点数:', historyLabels.length);
    }

    // 处理实时数据 - 只显示开盘时间的数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.lianban.length > 0) {
        // 显示完整的当天实时数据（包括完整的开盘时间段）
        console.log('连板实时数据数量:', realtimeDataCache.lianban.length);

        realtimeLabels = realtimeDataCache.lianban.map(item => item.time);

        // 显示所有级别合并，不分主要和高级别
        const allLevels = ['首板', '1进2', '2进3', '3进4', '4进5', '5进6', '6进7', '7进8', '8进9', '9进10'];

        // 只显示有数据的级别
        const levels = allLevels.filter(level =>
            realtimeDataCache.lianban.some(item => {
                const progress = item.data.lianban_progress;
                return progress && progress[level] && progress[level].includes('/');
            })
        );

        realtimeDatasets = levels.map((level, index) => {
            const data = realtimeDataCache.lianban.map(item => {
                const progress = item.data.lianban_progress;
                if (progress && progress[level]) {
                    const match = progress[level].match(/(\d+)\/(\d+)=(\d+)%/);
                    return match ? parseInt(match[3]) : 0;
                }
                return 0;
            });

            return {
                label: level + '(实时)',
                data: data,
                borderColor: getPyramidLevelColor(level),
                backgroundColor: getPyramidLevelColor(level, 0.4),
                fill: false,
                tension: 0.1
            };
        });

        console.log('实时数据处理完成，数据点数:', realtimeLabels.length);
    }

    // 调整标签分配，历史数据占一半空间，实时数据占一半空间
    let mergedLabels = [];
    const mergedDatasets = [];

    // 计算空间分配 - 固定总数量，确保50%-50%分配
    const totalPoints = 60; // 总数据点数
    const historyPoints = 30; // 历史数据占30个点
    const realtimePoints = 30; // 实时数据占30个点

    // 为历史数据创建均匀分布的标签
    const historySpacedLabels = [];
    for (let i = 0; i < historyPoints; i++) {
        if (historyLabels.length > 0) {
            // 均匀采样历史标签
            const index = Math.floor((i / (historyPoints - 1)) * (historyLabels.length - 1));
            historySpacedLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
        } else {
            historySpacedLabels.push('');
        }
    }

    // 为实时数据创建均匀分布的标签
    const realtimeSpacedLabels = [];
    for (let i = 0; i < realtimePoints; i++) {
        if (realtimeLabels.length > 0) {
            // 均匀采样实时标签
            const index = Math.floor((i / (realtimePoints - 1)) * (realtimeLabels.length - 1));
            realtimeSpacedLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
        } else {
            realtimeSpacedLabels.push('');
        }
    }

    // 合并标签
    mergedLabels = [...historySpacedLabels, ...realtimeSpacedLabels];

    // 合并每个级别的历史和实时数据
    if (historyDatasets.length > 0) {
        // 显示所有级别合并，不分主要和高级别
        const allLevels = ['首板', '1进2', '2进3', '3进4', '4进5', '5进6', '6进7', '7进8', '8进9', '9进10'];
        
        // 只显示有数据的级别
        const levels = allLevels.filter(level => 
            currentLianbanData.some(item => item.progress[level] && item.progress[level].total > 0)
        );

        levels.forEach((level, index) => {
            const historyData = historyDatasets.find(d => d.label === level)?.data || [];
            const realtimeData = realtimeDatasets.find(d => d.label === level + '(实时)')?.data || [];

            // 调整历史数据到30个点，均匀采样
            const historySpacedData = [];
            for (let i = 0; i < historyPoints; i++) {
                if (historyData.length > 0) {
                    const index = Math.floor((i / (historyPoints - 1)) * (historyData.length - 1));
                    historySpacedData.push(historyData[Math.min(index, historyData.length - 1)]);
                } else {
                    historySpacedData.push(null);
                }
            }

            // 调整实时数据到30个点，均匀采样
            const realtimeSpacedData = [];
            for (let i = 0; i < realtimePoints; i++) {
                if (realtimeData.length > 0) {
                    const index = Math.floor((i / (realtimePoints - 1)) * (realtimeData.length - 1));
                    realtimeSpacedData.push(realtimeData[Math.min(index, realtimeData.length - 1)]);
                } else {
                    realtimeSpacedData.push(null);
                }
            }

            // 合并数据
            const mergedData = [...historySpacedData, ...realtimeSpacedData];

            mergedDatasets.push({
                label: level,
                data: mergedData,
                borderColor: getPyramidLevelColor(level),
                backgroundColor: getPyramidLevelColor(level, 0.2),
                fill: false,
                tension: 0.1,
                spanGaps: true // 跨越null值
            });
        });
    }

    // 更新图表
    lianbanMergedChart.data.labels = mergedLabels;
    lianbanMergedChart.data.datasets = mergedDatasets;
    lianbanMergedChart.update();

    console.log('连板合并图表更新完成，总数据点数:', mergedLabels.length);
    console.log('图表数据集数量:', mergedDatasets.length);
    console.log('第一个数据集样本:', mergedDatasets[0] ? mergedDatasets[0].data.slice(0, 5) : '无数据');
}

// 更新市场涨跌合并图表
function updateMarketMergedChart() {
    if (!marketMergedChart) return;

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentMarketData && currentMarketData.length > 0) {
        historyLabels = currentMarketData.map(item => formatDate(item.date));

        historyDatasets = [
            {
                label: '上涨',
                data: currentMarketData.map(item => item.rise_count || 0),
                borderColor: '#dc3545', // 红色
                backgroundColor: '#dc3545' + '20',
                fill: false,
                tension: 0.1
            },
            {
                label: '下跌',
                data: currentMarketData.map(item => item.fall_count || 0),
                borderColor: '#28a745', // 绿色
                backgroundColor: '#28a745' + '20',
                fill: false,
                tension: 0.1
            }
        ];
    }

    // 处理实时数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.market.length > 0) {
        console.log('处理市场实时数据，缓存长度:', realtimeDataCache.market.length);
        console.log('实时数据样本:', realtimeDataCache.market.slice(0, 2));

        realtimeLabels = realtimeDataCache.market.map(item => item.time);

        realtimeDatasets = [
            {
                label: '上涨(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    let value = 0;
                    if (item.data) {
                        value = item.data.up_count || 0;
                    } else {
                        value = item.up_count || 0;
                    }
                    return value;
                }),
                borderColor: '#dc3545', // 红色
                backgroundColor: '#dc3545' + '40',
                fill: false,
                tension: 0.1
                // 移除虚线，使用实线
            },
            {
                label: '下跌(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    let value = 0;
                    if (item.data) {
                        value = item.data.down_count || 0;
                    } else {
                        value = item.down_count || 0;
                    }
                    return value;
                }),
                borderColor: '#28a745', // 绿色
                backgroundColor: '#28a745' + '40',
                fill: false,
                tension: 0.1
                // 移除虚线，使用实线
            }
        ];

        console.log('实时数据集创建完成，数据点数量:', realtimeDatasets[0].data.length);
    } else {
        console.log('没有市场实时数据缓存');
    }

    // 历史数据和实时数据各占一半空间
    const totalPoints = 60; // 总共60个点
    const historyPoints = 30; // 历史数据30个点
    const realtimePoints = 30; // 实时数据30个点

    // 处理历史数据 - 压缩到30个点
    let processedHistoryLabels = [];
    let processedHistoryDatasets = [];

    if (historyLabels.length > 0) {
        // 如果历史数据超过30个点，进行采样
        if (historyLabels.length > historyPoints) {
            const step = historyLabels.length / historyPoints;
            for (let i = 0; i < historyPoints; i++) {
                const index = Math.floor(i * step);
                processedHistoryLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
            }
        } else {
            // 如果历史数据不足30个点，补充空白
            processedHistoryLabels = [...historyLabels];
            while (processedHistoryLabels.length < historyPoints) {
                processedHistoryLabels.unshift('');
            }
        }

        // 处理历史数据集
        historyDatasets.forEach(dataset => {
            let processedData = [];
            if (dataset.data.length > historyPoints) {
                const step = dataset.data.length / historyPoints;
                for (let i = 0; i < historyPoints; i++) {
                    const index = Math.floor(i * step);
                    processedData.push(dataset.data[Math.min(index, dataset.data.length - 1)]);
                }
            } else {
                processedData = [...dataset.data];
                while (processedData.length < historyPoints) {
                    processedData.unshift(null);
                }
            }

            processedHistoryDatasets.push({
                ...dataset,
                data: [...processedData, ...new Array(realtimePoints).fill(null)]
            });
        });
    }

    // 处理实时数据 - 压缩到30个点
    let processedRealtimeLabels = [];
    let processedRealtimeDatasets = [];

    if (realtimeLabels.length > 0) {
        // 如果实时数据超过30个点，进行采样
        if (realtimeLabels.length > realtimePoints) {
            const step = realtimeLabels.length / realtimePoints;
            for (let i = 0; i < realtimePoints; i++) {
                const index = Math.floor(i * step);
                processedRealtimeLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
            }
        } else {
            // 如果实时数据不足30个点，补充空白
            processedRealtimeLabels = [...realtimeLabels];
            while (processedRealtimeLabels.length < realtimePoints) {
                processedRealtimeLabels.push('');
            }
        }

        // 处理实时数据集
        realtimeDatasets.forEach(dataset => {
            let processedData = [];
            if (dataset.data.length > realtimePoints) {
                const step = dataset.data.length / realtimePoints;
                for (let i = 0; i < realtimePoints; i++) {
                    const dataIndex = Math.floor(i * step);
                    processedData.push(dataset.data[Math.min(dataIndex, dataset.data.length - 1)]);
                }
            } else {
                processedData = [...dataset.data];
                while (processedData.length < realtimePoints) {
                    processedData.push(null);
                }
            }

            processedRealtimeDatasets.push({
                ...dataset,
                data: [...new Array(historyPoints).fill(null), ...processedData]
            });
        });
    }

    // 合并标签和数据集
    const mergedLabels = [...processedHistoryLabels, ...processedRealtimeLabels];
    const mergedDatasets = [...processedHistoryDatasets, ...processedRealtimeDatasets];

    console.log('市场合并图表:', {
        historyPoints: processedHistoryLabels.length,
        realtimePoints: processedRealtimeLabels.length,
        totalPoints: mergedLabels.length,
        datasets: mergedDatasets.length
    });

    marketMergedChart.data.labels = mergedLabels;
    marketMergedChart.data.datasets = mergedDatasets;
    marketMergedChart.update();
}

// 更新涨跌停合并图表
function updateLimitMergedChart() {
    if (!limitMergedChart) return;

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentLimitData && currentLimitData.length > 0) {
        historyLabels = currentLimitData.map(item => formatDate(item.date));

        historyDatasets = [
            {
                label: '涨停',
                data: currentLimitData.map(item => item.limit_up_total || 0),
                backgroundColor: 'transparent',
                borderColor: colors.danger,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.danger,
                pointBorderColor: colors.danger,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '跌停',
                data: currentLimitData.map(item => item.limit_down_total || 0),
                backgroundColor: 'transparent',
                borderColor: colors.success,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.success,
                pointBorderColor: colors.success,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '炸板数量',
                data: currentLimitData.map(item => item.zhaban_count || 0),
                backgroundColor: 'transparent',
                borderColor: colors.warning,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.warning,
                pointBorderColor: colors.warning,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '翘板数量',
                data: currentLimitData.map(item => item.qiaoban_count || 0),
                backgroundColor: 'transparent',
                borderColor: colors.purple,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.purple,
                pointBorderColor: colors.purple,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '一字涨停数量',
                data: currentLimitData.map(item => item.yizi_limit_up_count || 0),
                backgroundColor: 'transparent',
                borderColor: colors.pink,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.pink,
                pointBorderColor: colors.pink,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '一字跌停数量',
                data: currentLimitData.map(item => item.yizi_limit_down_count || 0),
                backgroundColor: 'transparent',
                borderColor: colors.darkred,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.darkred,
                pointBorderColor: colors.darkred,
                pointRadius: 3,
                pointHoverRadius: 5
            }
        ];
    }

    // 处理实时数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.market.length > 0) {
        realtimeLabels = realtimeDataCache.market.map(item => item.time);

        realtimeDatasets = [
            {
                label: '涨停(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    if (item.data) {
                        return item.data.limit_up_count || item.data.limit_up_total || item.data.limit_up || 0;
                    }
                    return item.limit_up_count || item.limit_up_total || item.limit_up || 0;
                }),
                backgroundColor: 'transparent',
                borderColor: colors.danger,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.danger,
                pointBorderColor: colors.danger,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '跌停(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    if (item.data) {
                        return item.data.limit_down_count || item.data.limit_down_total || item.data.limit_down || 0;
                    }
                    return item.limit_down_count || item.limit_down_total || item.limit_down || 0;
                }),
                backgroundColor: 'transparent',
                borderColor: colors.success,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.success,
                pointBorderColor: colors.success,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '炸板数量(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    if (item.data) {
                        return item.data.zhaban_count || 0;
                    }
                    return item.zhaban_count || 0;
                }),
                backgroundColor: 'transparent',
                borderColor: colors.warning,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.warning,
                pointBorderColor: colors.warning,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '翘板数量(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    if (item.data) {
                        return item.data.qiaoban_count || 0;
                    }
                    return item.qiaoban_count || 0;
                }),
                backgroundColor: 'transparent',
                borderColor: colors.purple,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.purple,
                pointBorderColor: colors.purple,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '一字涨停数量(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    if (item.data) {
                        return item.data.yizi_limit_up_count || 0;
                    }
                    return item.yizi_limit_up_count || 0;
                }),
                backgroundColor: 'transparent',
                borderColor: colors.pink,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.pink,
                pointBorderColor: colors.pink,
                pointRadius: 3,
                pointHoverRadius: 5
            },
            {
                label: '一字跌停数量(实时)',
                data: realtimeDataCache.market.map(item => {
                    // 兼容不同的数据结构
                    if (item.data) {
                        return item.data.yizi_limit_down_count || 0;
                    }
                    return item.yizi_limit_down_count || 0;
                }),
                backgroundColor: 'transparent',
                borderColor: colors.darkred,
                borderWidth: 2,
                fill: false,
                pointBackgroundColor: colors.darkred,
                pointBorderColor: colors.darkred,
                pointRadius: 3,
                pointHoverRadius: 5
            }
        ];
    }

    // 历史数据和实时数据各占一半空间
    const totalPoints = 60; // 总共60个点
    const historyPoints = 30; // 历史数据30个点
    const realtimePoints = 30; // 实时数据30个点

    // 处理历史数据 - 压缩到30个点
    let processedHistoryLabels = [];
    let processedHistoryDatasets = [];

    if (historyLabels.length > 0) {
        // 如果历史数据超过30个点，进行采样
        if (historyLabels.length > historyPoints) {
            const step = historyLabels.length / historyPoints;
            for (let i = 0; i < historyPoints; i++) {
                const index = Math.floor(i * step);
                processedHistoryLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
            }
        } else {
            // 如果历史数据不足30个点，补充空白
            processedHistoryLabels = [...historyLabels];
            while (processedHistoryLabels.length < historyPoints) {
                processedHistoryLabels.unshift('');
            }
        }

        // 处理历史数据集
        historyDatasets.forEach(dataset => {
            let processedData = [];
            if (dataset.data.length > historyPoints) {
                const step = dataset.data.length / historyPoints;
                for (let i = 0; i < historyPoints; i++) {
                    const index = Math.floor(i * step);
                    processedData.push(dataset.data[Math.min(index, dataset.data.length - 1)]);
                }
            } else {
                processedData = [...dataset.data];
                while (processedData.length < historyPoints) {
                    processedData.unshift(null);
                }
            }

            processedHistoryDatasets.push({
                ...dataset,
                data: [...processedData, ...new Array(realtimePoints).fill(null)]
            });
        });
    }

    // 处理实时数据 - 压缩到30个点
    let processedRealtimeLabels = [];
    let processedRealtimeDatasets = [];

    if (realtimeLabels.length > 0) {
        // 如果实时数据超过30个点，进行采样
        if (realtimeLabels.length > realtimePoints) {
            const step = realtimeLabels.length / realtimePoints;
            for (let i = 0; i < realtimePoints; i++) {
                const index = Math.floor(i * step);
                processedRealtimeLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
            }
        } else {
            // 如果实时数据不足30个点，补充空白
            processedRealtimeLabels = [...realtimeLabels];
            while (processedRealtimeLabels.length < realtimePoints) {
                processedRealtimeLabels.push('');
            }
        }

        // 处理实时数据集
        realtimeDatasets.forEach(dataset => {
            let processedData = [];
            if (dataset.data.length > realtimePoints) {
                const step = dataset.data.length / realtimePoints;
                for (let i = 0; i < realtimePoints; i++) {
                    const index = Math.floor(i * step);
                    processedData.push(dataset.data[Math.min(index, dataset.data.length - 1)]);
                }
            } else {
                processedData = [...dataset.data];
                while (processedData.length < realtimePoints) {
                    processedData.push(null);
                }
            }

            processedRealtimeDatasets.push({
                ...dataset,
                data: [...new Array(historyPoints).fill(null), ...processedData]
            });
        });
    }

    // 合并标签和数据集
    const mergedLabels = [...processedHistoryLabels, ...processedRealtimeLabels];
    const mergedDatasets = [...processedHistoryDatasets, ...processedRealtimeDatasets];

    limitMergedChart.data.labels = mergedLabels;
    limitMergedChart.data.datasets = mergedDatasets;
    limitMergedChart.update();
}

// 更新成交金额合并图表
function updateVolumeMergedChart() {
    if (!volumeMergedChart) return;

    // 处理历史数据
    let historyLabels = [];
    let historyDatasets = [];

    if (currentVolumeData && currentVolumeData.length > 0) {
        historyLabels = currentVolumeData.map(item => formatDate(item.date));

        historyDatasets = [{
            label: '成交金额',
            data: currentVolumeData.map(item => {
                // 使用total_amount字段，单位已经是分，转换为亿
                return (item.volume || item.total_amount || 0) / 100000000;
            }),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            fill: true,
            tension: 0.1
        }];
    }

    // 处理实时数据
    let realtimeLabels = [];
    let realtimeDatasets = [];

    if (realtimeDataCache.market.length > 0) {
        realtimeLabels = realtimeDataCache.market.map(item => item.time);

        realtimeDatasets = [{
            label: '成交金额(实时)',
            data: realtimeDataCache.market.map(item => {
                // 兼容不同的数据结构
                let volume = 0;
                if (item.data) {
                    volume = item.data.volume || item.data.total_amount || 0;
                } else {
                    volume = item.volume || item.total_amount || 0;
                }

                if (typeof volume === 'string') {
                    // 解析类似 "1.2万亿" 的格式
                    if (volume.includes('万亿')) {
                        return parseFloat(volume.replace('万亿', '')) * 10000;
                    } else if (volume.includes('亿')) {
                        return parseFloat(volume.replace('亿', ''));
                    } else if (volume.includes('万')) {
                        return parseFloat(volume.replace('万', '')) / 10000; // 转换为亿
                    }
                }
                // 如果是数字，转换为亿
                return (parseFloat(volume) || 0) / 100000000;
            }),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '40',
            fill: true,
            tension: 0.1
            // 移除虚线，使用实线
        }];
    }

    // 历史数据和实时数据各占一半空间
    const totalPoints = 60; // 总共60个点
    const historyPoints = 30; // 历史数据30个点
    const realtimePoints = 30; // 实时数据30个点

    // 处理历史数据 - 压缩到30个点
    let processedHistoryLabels = [];
    let processedHistoryDatasets = [];

    if (historyLabels.length > 0) {
        // 如果历史数据超过30个点，进行采样
        if (historyLabels.length > historyPoints) {
            const step = historyLabels.length / historyPoints;
            for (let i = 0; i < historyPoints; i++) {
                const index = Math.floor(i * step);
                processedHistoryLabels.push(historyLabels[Math.min(index, historyLabels.length - 1)]);
            }
        } else {
            // 如果历史数据不足30个点，补充空白
            processedHistoryLabels = [...historyLabels];
            while (processedHistoryLabels.length < historyPoints) {
                processedHistoryLabels.unshift('');
            }
        }

        // 处理历史数据集
        historyDatasets.forEach(dataset => {
            let processedData = [];
            if (dataset.data.length > historyPoints) {
                const step = dataset.data.length / historyPoints;
                for (let i = 0; i < historyPoints; i++) {
                    const index = Math.floor(i * step);
                    processedData.push(dataset.data[Math.min(index, dataset.data.length - 1)]);
                }
            } else {
                processedData = [...dataset.data];
                while (processedData.length < historyPoints) {
                    processedData.unshift(null);
                }
            }

            processedHistoryDatasets.push({
                ...dataset,
                data: [...processedData, ...new Array(realtimePoints).fill(null)]
            });
        });
    }

    // 处理实时数据 - 压缩到30个点
    let processedRealtimeLabels = [];
    let processedRealtimeDatasets = [];

    if (realtimeLabels.length > 0) {
        // 如果实时数据超过30个点，进行采样
        if (realtimeLabels.length > realtimePoints) {
            const step = realtimeLabels.length / realtimePoints;
            for (let i = 0; i < realtimePoints; i++) {
                const index = Math.floor(i * step);
                processedRealtimeLabels.push(realtimeLabels[Math.min(index, realtimeLabels.length - 1)]);
            }
        } else {
            // 如果实时数据不足30个点，补充空白
            processedRealtimeLabels = [...realtimeLabels];
            while (processedRealtimeLabels.length < realtimePoints) {
                processedRealtimeLabels.push('');
            }
        }

        // 处理实时数据集
        realtimeDatasets.forEach(dataset => {
            let processedData = [];
            if (dataset.data.length > realtimePoints) {
                const step = dataset.data.length / realtimePoints;
                for (let i = 0; i < realtimePoints; i++) {
                    const index = Math.floor(i * step);
                    processedData.push(dataset.data[Math.min(index, dataset.data.length - 1)]);
                }
            } else {
                processedData = [...dataset.data];
                while (processedData.length < realtimePoints) {
                    processedData.push(null);
                }
            }

            processedRealtimeDatasets.push({
                ...dataset,
                data: [...new Array(historyPoints).fill(null), ...processedData]
            });
        });
    }

    // 合并标签和数据集
    const mergedLabels = [...processedHistoryLabels, ...processedRealtimeLabels];
    const mergedDatasets = [...processedHistoryDatasets, ...processedRealtimeDatasets];

    volumeMergedChart.data.labels = mergedLabels;
    volumeMergedChart.data.datasets = mergedDatasets;
    volumeMergedChart.update();
}
