#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据可视化系统配置文件
"""

import os

class Config:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'stock_data_visualization_2025'
    JSON_AS_ASCII = False
    
    # 数据文件路径
    DABANKE_DATA_PATH = os.environ.get('DABANKE_DATA_PATH') or './dabanke_data'
    MARKET_DATA_PATH = os.environ.get('MARKET_DATA_PATH') or './market_data'
    
    # 服务器配置
    HOST = os.environ.get('FLASK_HOST') or '0.0.0.0'
    PORT = int(os.environ.get('FLASK_PORT') or 5000)
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() in ['true', '1', 'yes']
    
    # 数据刷新间隔（秒）
    DATA_REFRESH_INTERVAL = int(os.environ.get('DATA_REFRESH_INTERVAL') or 300)  # 5分钟
    
    # 图表配置
    CHART_COLORS = {
        'primary': '#007bff',
        'success': '#28a745',
        'warning': '#ffc107',
        'danger': '#dc3545',
        'info': '#17a2b8',
        'light': '#f8f9fa',
        'dark': '#343a40'
    }
    
    # 连板级别配置
    LIANBAN_LEVELS = ['首板', '1进2', '2进3', '3进4', '5进6', '9进10']
    
    # 数据缓存配置
    ENABLE_CACHE = os.environ.get('ENABLE_CACHE', 'True').lower() in ['true', '1', 'yes']
    CACHE_TIMEOUT = int(os.environ.get('CACHE_TIMEOUT') or 300)  # 5分钟
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'app.log'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    HOST = '127.0.0.1'  # 生产环境建议只监听本地
    
class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
