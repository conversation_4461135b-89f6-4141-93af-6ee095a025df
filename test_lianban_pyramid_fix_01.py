#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序01：验证连板金字塔修复效果
测试连板金字塔是否能正确显示所有连板级别的股票
"""

import requests
import json
import time
from datetime import datetime

def test_lianban_api():
    """测试连板数据API"""
    print("=" * 60)
    print("测试程序01：验证连板金字塔修复效果")
    print("=" * 60)
    
    # 测试API端点
    api_url = "http://localhost:8080/api/lianban_progress"
    
    try:
        print(f"🔍 正在请求API: {api_url}")
        response = requests.get(api_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API请求成功，状态码: {response.status_code}")
            
            # 检查数据结构
            if data.get('success'):
                progress_data = data.get('data', [])
                latest_stocks = data.get('latest_stocks', {})
                
                print(f"📊 历史数据天数: {len(progress_data)}")
                print(f"📈 最新股票数据级别数: {len(latest_stocks) if latest_stocks else 0}")
                
                if latest_stocks:
                    print("\n🎯 各连板级别股票数量:")
                    total_stocks = 0
                    for level, stocks in latest_stocks.items():
                        if stocks:  # 只显示有股票的级别
                            stock_count = len(stocks)
                            total_stocks += stock_count
                            print(f"   {level}: {stock_count}只股票")
                    
                    print(f"\n📊 总股票数: {total_stocks}")
                    
                    # 检查是否有多个级别的数据
                    non_empty_levels = [level for level, stocks in latest_stocks.items() if stocks]
                    print(f"🔍 有数据的连板级别: {len(non_empty_levels)}个")
                    print(f"📋 级别列表: {non_empty_levels}")
                    
                    if len(non_empty_levels) >= 3:
                        print("✅ 修复成功！金字塔应该能显示多个连板级别")
                    else:
                        print("⚠️  可能仍有问题，连板级别数量较少")
                        
                else:
                    print("❌ 没有最新股票数据")
                    
            else:
                print(f"❌ API返回失败: {data}")
                
        else:
            print(f"❌ API请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")

def test_web_page():
    """测试网页是否正常加载"""
    print("\n" + "=" * 60)
    print("测试网页访问")
    print("=" * 60)
    
    web_url = "http://localhost:8080"
    
    try:
        print(f"🌐 正在访问网页: {web_url}")
        response = requests.get(web_url, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ 网页访问成功，状态码: {response.status_code}")
            
            # 检查页面内容是否包含关键元素
            content = response.text
            if "连板金字塔" in content:
                print("✅ 页面包含连板金字塔元素")
            else:
                print("⚠️  页面可能缺少连板金字塔元素")
                
        else:
            print(f"❌ 网页访问失败，状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网页访问异常: {e}")

def main():
    """主函数"""
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试API
    test_lianban_api()
    
    # 测试网页
    test_web_page()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n请在浏览器中访问 http://localhost:8080 查看连板金字塔是否正常显示所有级别")

if __name__ == "__main__":
    main()
